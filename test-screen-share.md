# 屏幕共享流显示问题修复

## 问题描述
共享屏幕看不到流，原因是元素宽高为0，需要根据视频流改进。

## 修复方案

### 1. 新增获取视频流实际尺寸的函数
```typescript
async function getStreamVideoSize(stream: any): Promise<{width: number, height: number}>
```
- 从视频轨道的 `getSettings()` 方法获取实际的宽高
- 支持多种流对象结构（getMediaStream、stream、_stream）
- 如果无法获取，返回默认值 1920x1080

### 2. 改进播放流函数
```typescript
async function playStream(el: HTMLElement, stream: any, options?: {...})
```
- 对屏幕共享特殊处理，获取视频流实际尺寸
- 当容器尺寸为0时，使用视频流实际尺寸设置容器
- 动态设置容器的 minWidth、minHeight、width、height

### 3. 新增专门的屏幕共享播放函数
```typescript
async function playScreenShareStream(el: HTMLElement, stream: any)
```
- 专门处理屏幕共享流的播放
- 确保容器有正确的样式和尺寸
- 使用视频流实际尺寸作为渲染模式
- 包含错误处理和回退机制

### 4. 改进video元素样式设置
```typescript
function forceSetVideoElementStyle(videoElement: HTMLVideoElement, container: HTMLElement, uid: string)
```
- 监听 `loadedmetadata` 事件获取视频实际尺寸
- 根据视频尺寸动态调整容器和video元素
- 设置合适的最小尺寸和最大尺寸

### 5. 更新CSS样式
- 屏幕共享容器使用 flex 布局居中显示
- 设置合适的最小尺寸
- video元素使用 `object-fit: contain` 保持比例

## 主要改进点

1. **动态尺寸计算**: 根据视频流的实际尺寸动态设置容器尺寸
2. **零尺寸处理**: 当容器尺寸为0时，使用视频流尺寸作为基准
3. **异步尺寸获取**: 通过 `loadedmetadata` 事件获取准确的视频尺寸
4. **错误处理**: 包含完整的错误处理和回退机制
5. **样式优化**: 改进CSS样式确保正确显示

## 测试建议

1. 测试屏幕共享开启时容器是否正确显示
2. 测试不同分辨率的屏幕共享
3. 测试容器初始尺寸为0的情况
4. 测试视频流加载过程中的尺寸变化
